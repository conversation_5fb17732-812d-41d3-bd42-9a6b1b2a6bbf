import 'package:flutter_test/flutter_test.dart';
import 'package:taal_connect/services/singer_service.dart';

void main() {
  group('Final Dashboard Data Tests', () {
    late SingerService singerService;

    setUp(() {
      singerService = SingerService();
    });

    test('should fetch featured singers for dashboard', () async {
      try {
        final featuredSingers = await singerService.getFeaturedSingers();
        
        print('✅ Featured singers fetched: ${featuredSingers.length} singers');
        expect(featuredSingers.length, greaterThan(0));
        
        for (final singer in featuredSingers) {
          print('🎤 ${singer.fullName} - ₹${singer.hourlyRate}/hr - ${singer.rating}★ (${singer.reviewCount} reviews)');
          print('   Genres: ${singer.genres.join(', ')}');
          print('   ${singer.description}');
          print('');
          
          expect(singer.fullName, isNotEmpty);
          expect(singer.hourlyRate, greaterThan(0));
          expect(singer.rating, greaterThan(0));
          expect(singer.genres, isNotEmpty);
        }
        
      } catch (e) {
        fail('Featured singers test failed: $e');
      }
    });

    test('should fetch top rated singers for dashboard', () async {
      try {
        final topRatedSingers = await singerService.getTopRatedSingers();
        
        print('✅ Top rated singers fetched: ${topRatedSingers.length} singers');
        expect(topRatedSingers.length, greaterThan(0));
        
        for (final singer in topRatedSingers) {
          print('⭐ ${singer.fullName} - ${singer.rating}★ rating');
        }
        
      } catch (e) {
        fail('Top rated singers test failed: $e');
      }
    });

    test('should fetch categories for dashboard', () async {
      try {
        final categories = await singerService.getCategories();
        
        print('✅ Categories fetched: ${categories.length} categories');
        print('📂 Categories: ${categories.join(', ')}');
        
        expect(categories.length, greaterThan(0));
        expect(categories, contains('Classical'));
        expect(categories, contains('Pop'));
        expect(categories, contains('Rock'));
        
      } catch (e) {
        fail('Categories test failed: $e');
      }
    });
  });
}

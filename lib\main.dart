import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:taal_connect/screens/splash_screen.dart';
import 'package:taal_connect/services/auth_service.dart';
import 'package:taal_connect/services/http_service.dart';
import 'package:taal_connect/utils/theme.dart';

void main() {
  // Initialize HTTP service
  HttpService().init();

  runApp(
    MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthService()),
      ],
      child: const MyApp(),
    ),
  );
}

class MyApp extends StatelessWidget {
  const MyApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'TaalConnect',
      debugShowCheckedModeBanner: false,
      theme: AppTheme.lightTheme,
      home: const SplashScreen(),
    );
  }
}

import 'package:flutter_test/flutter_test.dart';
import 'package:taal_connect/services/singer_service.dart';
import 'package:taal_connect/services/http_service.dart';
import 'dart:convert';

void main() {
  group('SingerService Tests', () {
    late SingerService singerService;
    late HttpService httpService;

    setUp(() {
      httpService = HttpService();
      httpService.init();
      singerService = SingerService();
    });

    tearDown(() {
      httpService.dispose();
    });

    test('should fetch featured singers successfully', () async {
      try {
        // First login to get a token
        final loginResponse = await httpService.post(
          'http://localhost:8000/api/auth/login',
          body: jsonEncode({
            'email': '<EMAIL>',
            'password': 'test123',
          }),
        );

        expect(loginResponse.statusCode, equals(200));
        print('✅ Login successful');

        // Now try to fetch featured singers
        final featuredSingers = await singerService.getFeaturedSingers();
        
        print('✅ Featured singers fetched: ${featuredSingers.length} singers');
        
        // We should have some singers from our sample data
        expect(featuredSingers.length, greaterThan(0));
        
        // Check that each singer has required fields
        for (final singer in featuredSingers) {
          expect(singer.fullName, isNotEmpty);
          expect(singer.email, isNotEmpty);
          expect(singer.hourlyRate, greaterThan(0));
          print('Singer: ${singer.fullName} - Rate: \$${singer.hourlyRate}/hr');
        }
        
      } catch (e) {
        fail('Featured singers test failed: $e');
      }
    });

    test('should fetch categories successfully', () async {
      try {
        // Login first
        final loginResponse = await httpService.post(
          'http://localhost:8000/api/auth/login',
          body: jsonEncode({
            'email': '<EMAIL>',
            'password': 'test123',
          }),
        );

        expect(loginResponse.statusCode, equals(200));

        // Fetch categories
        final categories = await singerService.getCategories();
        
        print('✅ Categories fetched: ${categories.length} categories');
        print('Categories: ${categories.join(', ')}');
        
        // We should have some categories from our sample data
        expect(categories.length, greaterThan(0));
        
        // Check for some expected categories
        expect(categories, contains('Classical'));
        expect(categories, contains('Pop'));
        
      } catch (e) {
        fail('Categories test failed: $e');
      }
    });

    test('should fetch top rated singers successfully', () async {
      try {
        // Login first
        final loginResponse = await httpService.post(
          'http://localhost:8000/api/auth/login',
          body: jsonEncode({
            'email': '<EMAIL>',
            'password': 'test123',
          }),
        );

        expect(loginResponse.statusCode, equals(200));

        // Fetch top rated singers
        final topRatedSingers = await singerService.getTopRatedSingers();
        
        print('✅ Top rated singers fetched: ${topRatedSingers.length} singers');
        
        // We should have some singers
        expect(topRatedSingers.length, greaterThan(0));
        
        // Check that singers are sorted by rating (highest first)
        for (int i = 0; i < topRatedSingers.length - 1; i++) {
          expect(topRatedSingers[i].rating, 
                 greaterThanOrEqualTo(topRatedSingers[i + 1].rating));
        }
        
        for (final singer in topRatedSingers) {
          print('Singer: ${singer.fullName} - Rating: ${singer.rating}/5.0');
        }
        
      } catch (e) {
        fail('Top rated singers test failed: $e');
      }
    });
  });
}

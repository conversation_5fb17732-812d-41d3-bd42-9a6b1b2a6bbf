const http = require('http');

const server = http.createServer((req, res) => {
  res.writeHead(200, { 'Content-Type': 'application/json' });
  res.end(JSON.stringify({ message: 'Test server working!' }));
});

server.listen(8001, '0.0.0.0', () => {
  console.log('Test server running on http://0.0.0.0:8001');
  console.log('Try accessing:');
  console.log('- http://localhost:8001');
  console.log('- http://***********:8001');
});

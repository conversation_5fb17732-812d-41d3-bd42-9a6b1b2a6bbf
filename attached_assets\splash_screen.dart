import 'dart:async';

import 'package:flutter/material.dart';
import 'package:taalconnect/login.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    Timer(const Duration(seconds: 4), () {
      Navigator.push(context, MaterialPageRoute(builder: (context) => Login()));
    });
  }

  @override
  Widget build(BuildContext context) {
    return const Scaffold(
        body: Image(
            fit: BoxFit.fitHeight,
            width: double.infinity,
            height: double.infinity,
            image: AssetImage('image/SplashScreen.png')));
  }
}

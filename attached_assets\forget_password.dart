import 'package:flutter/material.dart';
import 'package:taalconnect/forget_pass_pagetwo.dart';

class ForgetPassword extends StatefulWidget {
  const ForgetPassword({super.key});

  @override
  State<ForgetPassword> createState() => _ForgetPasswordState();
}

class _ForgetPasswordState extends State<ForgetPassword> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xffCFD9BA),
      appBar: AppBar(
        backgroundColor: const Color(0xffCFD9BA),
        title: const Text('back'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          children: [
            const Text(
              'Forget\nPassword?',
              style: TextStyle(
                fontSize: 50,
              ),
            ),
            const SizedBox(
              height: 20,
            ),
            const Text(
                'Enter your email for the verification process,we will send code to your email'),
            const SizedBox(
              height: 40,
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20.0),
              child: Text<PERSON><PERSON><PERSON><PERSON>(
                decoration: InputDecoration(
                    filled: true,
                    fillColor: const Color(0xffF2EFDF),
                    labelStyle: const TextStyle(color: Color(0xff425951)),
                    hintStyle: const TextStyle(color: Color(0xff425951)),
                    border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(20)),
                    enabledBorder: OutlineInputBorder(
                        borderSide: const BorderSide(color: Color(0xffF2EFDF)),
                        borderRadius: BorderRadius.circular(20)),
                    focusedBorder: OutlineInputBorder(
                        borderSide: const BorderSide(color: Color(0xff425951)),
                        borderRadius: BorderRadius.circular(20)),
                    hintText: 'Email',
                    labelText: 'Email'),
              ),
            ),
            const SizedBox(
              height: 5,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Don\'t have an account? ',
                  style: TextStyle(fontSize: 14, color: Colors.grey),
                ),
                InkWell(
                    onTap: () {},
                    child: const Text(
                      'Resend',
                      style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: Color(0xff5A7364)),
                    )),
              ],
            ),
            const SizedBox(
              height: 30,
            ),
            ElevatedButton(
                onPressed: () {
                  Navigator.push(
                      context,
                      MaterialPageRoute(
                          builder: (context) => ForgetPassPagetwo()));
                },
                style: ElevatedButton.styleFrom(
                  minimumSize: const Size(220, 36),
                  backgroundColor: const Color(0xff425951), // Button color
                  foregroundColor: const Color(0xffFFFFFF), // Text color
                  padding:
                      const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                  elevation: 5, // Shadow effect
                ),
                child: const Text('Continue')),
          ],
        ),
      ),
    );
  }
}

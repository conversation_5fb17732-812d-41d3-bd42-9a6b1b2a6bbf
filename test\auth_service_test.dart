import 'package:flutter_test/flutter_test.dart';
import 'package:taal_connect/services/auth_service.dart';
import 'package:taal_connect/services/http_service.dart';
import 'package:taal_connect/models/user.dart';
import 'dart:convert';

void main() {
  group('AuthService Role Update Tests', () {
    late AuthService authService;
    late HttpService httpService;

    setUp(() {
      httpService = HttpService();
      httpService.init();
      authService = AuthService();
    });

    tearDown(() {
      httpService.dispose();
    });

    test('should successfully update user role with valid token', () async {
      // First, create a test user and get a valid token
      try {
        final registerResponse = await httpService.post(
          'http://localhost:8000/api/auth/register',
          body: jsonEncode({
            'full_name': 'Role Test User',
            'email': '<EMAIL>',
            'password': 'test123',
            'phone_number': '1234567890',
            'cnic_number': '12345-1234567-1',
          }),
        );

        expect(registerResponse.statusCode, equals(201));
        final userData = jsonDecode(registerResponse.body);
        final user = User.fromJson(userData);

        print('Created test user: ${user.email} with role: ${user.role}');
        expect(user.role, equals('customer')); // Default role

        // Set the current user in auth service by simulating login
        // We'll use reflection or create a test-specific method
        // For now, let's test the login flow first
        final loginResponse = await httpService.post(
          'http://localhost:8000/api/auth/login',
          body: jsonEncode({
            'email': '<EMAIL>',
            'password': 'test123',
          }),
        );

        expect(loginResponse.statusCode, equals(200));
        final loginData = jsonDecode(loginResponse.body);
        final loggedInUser = User.fromJson(loginData);

        // Manually set the user for testing (we'll need to make this field accessible)
        // For now, let's test with a direct API call

        // Now try to update the role to singer
        final success = await authService.updateUserRole('singer');

        expect(success, isTrue);
        expect(authService.currentUser?.role, equals('singer'));
        print('Successfully updated role to: ${authService.currentUser?.role}');
      } catch (e) {
        fail('Role update test failed: $e');
      }
    });

    test('should fail to update role with invalid token', () async {
      // Create a user with an invalid token
      final invalidUser = User(
        id: 999,
        fullName: 'Invalid User',
        email: '<EMAIL>',
        phoneNumber: '1234567890',
        cnicNumber: '12345-1234567-1',
        role: 'customer',
        token: 'invalid_token_123',
      );

      authService._currentUser = invalidUser;

      // Try to update role with invalid token
      final success = await authService.updateUserRole('singer');

      expect(success, isFalse);
      print('Correctly failed to update role with invalid token');
    });

    test('should fail to update role when no user is logged in', () async {
      // Ensure no user is set
      authService._currentUser = null;

      // Try to update role
      final success = await authService.updateUserRole('singer');

      expect(success, isFalse);
      print('Correctly failed to update role when no user is logged in');
    });

    test('should handle server errors gracefully', () async {
      // Create a user with a valid-looking but expired token
      final expiredUser = User(
        id: 999,
        fullName: 'Expired User',
        email: '<EMAIL>',
        phoneNumber: '1234567890',
        cnicNumber: '12345-1234567-1',
        role: 'customer',
        token:
            'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************.invalid',
      );

      authService._currentUser = expiredUser;

      // Try to update role
      final success = await authService.updateUserRole('singer');

      expect(success, isFalse);
      print('Correctly handled server error for expired token');
    });
  });
}

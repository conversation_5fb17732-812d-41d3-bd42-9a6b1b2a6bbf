const db = require('../db/db_config');

// Initialize Stripe only if the secret key is provided
let stripe = null;
if (process.env.STRIPE_SECRET_KEY && process.env.STRIPE_SECRET_KEY !== 'your_stripe_secret_key') {
  stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
}

// Process a payment for a booking
exports.processPayment = async (req, res) => {
  try {
    const { booking_id, amount, payment_method = 'card' } = req.body;
    const userId = req.user.id;
    
    // Validate required fields
    if (!booking_id || amount === undefined) {
      return res.status(400).json({ message: 'Booking ID and amount are required' });
    }
    
    // Get the booking
    const booking = await db.getOne('SELECT * FROM bookings WHERE id = ?', [booking_id]);
    
    if (!booking) {
      return res.status(404).json({ message: 'Booking not found' });
    }
    
    // Check if the user is the customer of this booking
    if (booking.customer_id !== userId && req.user.role !== 'admin') {
      return res.status(403).json({ message: 'You can only process payments for your own bookings' });
    }
    
    // Check if booking is in pending or confirmed status
    if (!['pending', 'confirmed'].includes(booking.status)) {
      return res.status(400).json({ message: 'Payment can only be processed for pending or confirmed bookings' });
    }
    
    // Check if payment already exists
    const existingPayment = await db.getOne(
      'SELECT * FROM payments WHERE booking_id = ? AND status IN (\'completed\', \'pending\')',
      [booking_id]
    );
    
    if (existingPayment) {
      return res.status(400).json({ message: 'Payment already exists for this booking' });
    }
    
    let transaction_id = null;
    
    // In a real implementation, process the payment with Stripe or another payment processor
    if (stripe) {
      try {
        // Create a payment intent with Stripe
        const paymentIntent = await stripe.paymentIntents.create({
          amount: Math.round(amount * 100), // Convert to cents for Stripe
          currency: 'usd',
          description: `Booking #${booking_id} - TaalConnect Singer Booking`,
          metadata: {
            booking_id: booking_id,
            customer_id: userId
          }
        });
        
        transaction_id = paymentIntent.id;
      } catch (stripeError) {
        console.error('Stripe payment error:', stripeError);
        return res.status(400).json({ message: `Payment processing error: ${stripeError.message}` });
      }
    } else {
      // For testing/development, create a mock transaction ID
      transaction_id = `mock-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
    }
    
    // Create the payment record
    const paymentId = await db.insertAndGetId(
      'INSERT INTO payments (booking_id, amount, payment_method, transaction_id, status) VALUES (?, ?, ?, ?, ?)',
      [booking_id, amount, payment_method, transaction_id, 'pending']
    );
    
    // Update booking payment status
    await db.executeQuery(
      'UPDATE bookings SET payment_status = \'pending\' WHERE id = ?',
      [booking_id]
    );
    
    // Get the created payment
    const payment = await db.getOne('SELECT * FROM payments WHERE id = ?', [paymentId]);
    
    res.status(201).json({
      payment_id: payment.id,
      booking_id: payment.booking_id,
      amount: payment.amount,
      status: payment.status,
      transaction_id: payment.transaction_id,
      created_at: payment.created_at
    });
  } catch (error) {
    console.error('Process payment error:', error);
    res.status(500).json({ message: 'Server error while processing payment' });
  }
};

// Get payment methods
exports.getPaymentMethods = async (req, res) => {
  try {
    // In a real application, you would retrieve the user's stored payment methods
    // For now, return default payment methods
    const paymentMethods = [
      {
        id: 'card',
        name: 'Credit/Debit Card',
        is_default: true
      },
      {
        id: 'bank',
        name: 'Bank Transfer',
        is_default: false
      }
    ];
    
    res.status(200).json(paymentMethods);
  } catch (error) {
    console.error('Get payment methods error:', error);
    res.status(500).json({ message: 'Server error while fetching payment methods' });
  }
};

// Add payment method
exports.addPaymentMethod = async (req, res) => {
  try {
    const { payment_method_id, token } = req.body;
    
    if (!payment_method_id || !token) {
      return res.status(400).json({ message: 'Payment method ID and token are required' });
    }
    
    // In a real application, you would store the payment method with Stripe or another provider
    // For now, just acknowledge the request
    
    res.status(201).json({ message: 'Payment method added successfully' });
  } catch (error) {
    console.error('Add payment method error:', error);
    res.status(500).json({ message: 'Server error while adding payment method' });
  }
};

// Get payment history
exports.getPaymentHistory = async (req, res) => {
  try {
    const userId = req.user.id;
    
    // If user is a customer, get payments for their bookings
    // If user is a singer, get payments for bookings they were booked for
    let payments = [];
    
    if (req.user.role === 'customer') {
      payments = await db.executeQuery(`
        SELECT 
          p.*, b.booking_date, b.venue, b.event_type,
          s.id as singer_id, su.full_name as singer_name
        FROM payments p
        JOIN bookings b ON p.booking_id = b.id
        JOIN singers s ON b.singer_id = s.id
        JOIN users su ON s.user_id = su.id
        WHERE b.customer_id = ?
        ORDER BY p.created_at DESC
      `, [userId]);
    } else if (req.user.role === 'singer') {
      // Get singer ID
      const singer = await db.getOne('SELECT id FROM singers WHERE user_id = ?', [userId]);
      
      if (singer) {
        payments = await db.executeQuery(`
          SELECT 
            p.*, b.booking_date, b.venue, b.event_type,
            cu.id as customer_id, cu.full_name as customer_name
          FROM payments p
          JOIN bookings b ON p.booking_id = b.id
          JOIN users cu ON b.customer_id = cu.id
          WHERE b.singer_id = ?
          ORDER BY p.created_at DESC
        `, [singer.id]);
      }
    } else if (req.user.role === 'admin') {
      // Admin can see all payments
      payments = await db.executeQuery(`
        SELECT 
          p.*, b.booking_date, b.venue, b.event_type,
          cu.id as customer_id, cu.full_name as customer_name,
          s.id as singer_id, su.full_name as singer_name
        FROM payments p
        JOIN bookings b ON p.booking_id = b.id
        JOIN users cu ON b.customer_id = cu.id
        JOIN singers s ON b.singer_id = s.id
        JOIN users su ON s.user_id = su.id
        ORDER BY p.created_at DESC
      `);
    }
    
    // Format the response
    const formattedPayments = payments.map(p => ({
      id: p.id,
      booking_id: p.booking_id,
      amount: p.amount,
      payment_method: p.payment_method,
      status: p.status,
      created_at: p.created_at,
      booking_date: p.booking_date,
      event_type: p.event_type,
      venue: p.venue,
      customer_name: p.customer_name,
      singer_name: p.singer_name
    }));
    
    res.status(200).json(formattedPayments);
  } catch (error) {
    console.error('Get payment history error:', error);
    res.status(500).json({ message: 'Server error while fetching payment history' });
  }
};

// Request refund
exports.requestRefund = async (req, res) => {
  try {
    const { id } = req.params;
    const { reason } = req.body;
    const userId = req.user.id;
    
    // Get the payment
    const payment = await db.getOne('SELECT * FROM payments WHERE id = ?', [id]);
    
    if (!payment) {
      return res.status(404).json({ message: 'Payment not found' });
    }
    
    // Get the booking
    const booking = await db.getOne('SELECT * FROM bookings WHERE id = ?', [payment.booking_id]);
    
    if (!booking) {
      return res.status(404).json({ message: 'Associated booking not found' });
    }
    
    // Check authorization
    // Only customers who made the payment, or admins, can request refunds
    if (booking.customer_id !== userId && req.user.role !== 'admin') {
      return res.status(403).json({ message: 'You are not authorized to request a refund for this payment' });
    }
    
    // Check if payment is eligible for refund
    if (payment.status !== 'completed' && payment.status !== 'pending') {
      return res.status(400).json({ message: 'This payment is not eligible for refund' });
    }
    
    // In a real application, process refund with payment provider
    if (stripe && payment.transaction_id && !payment.transaction_id.startsWith('mock-')) {
      try {
        await stripe.refunds.create({
          payment_intent: payment.transaction_id,
          reason: 'requested_by_customer'
        });
      } catch (stripeError) {
        console.error('Stripe refund error:', stripeError);
        return res.status(400).json({ message: `Refund processing error: ${stripeError.message}` });
      }
    }
    
    // Update payment status
    await db.executeQuery(
      'UPDATE payments SET status = \'refunded\' WHERE id = ?',
      [id]
    );
    
    // Update booking payment status
    await db.executeQuery(
      'UPDATE bookings SET payment_status = \'refunded\' WHERE id = ?',
      [payment.booking_id]
    );
    
    // If booking is still pending or confirmed, cancel it
    if (['pending', 'confirmed'].includes(booking.status)) {
      let updatedNotes = booking.notes || '';
      if (reason) {
        updatedNotes += updatedNotes ? `\n\nRefund reason: ${reason}` : `Refund reason: ${reason}`;
      }
      
      await db.executeQuery(
        'UPDATE bookings SET status = \'cancelled\', notes = ? WHERE id = ?',
        [updatedNotes, payment.booking_id]
      );
    }
    
    res.status(200).json({ message: 'Refund processed successfully' });
  } catch (error) {
    console.error('Request refund error:', error);
    res.status(500).json({ message: 'Server error while processing refund request' });
  }
};

// Get payment details
exports.getPaymentDetails = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;
    
    // Get the payment with booking and user details
    let payment;
    
    if (req.user.role === 'admin') {
      // Admin can see any payment
      payment = await db.getOne(`
        SELECT 
          p.*, b.booking_date, b.venue, b.event_type, b.status as booking_status,
          cu.id as customer_id, cu.full_name as customer_name,
          s.id as singer_id, su.full_name as singer_name
        FROM payments p
        JOIN bookings b ON p.booking_id = b.id
        JOIN users cu ON b.customer_id = cu.id
        JOIN singers s ON b.singer_id = s.id
        JOIN users su ON s.user_id = su.id
        WHERE p.id = ?
      `, [id]);
    } else if (req.user.role === 'customer') {
      // Customer can only see their own payments
      payment = await db.getOne(`
        SELECT 
          p.*, b.booking_date, b.venue, b.event_type, b.status as booking_status,
          cu.id as customer_id, cu.full_name as customer_name,
          s.id as singer_id, su.full_name as singer_name
        FROM payments p
        JOIN bookings b ON p.booking_id = b.id
        JOIN users cu ON b.customer_id = cu.id
        JOIN singers s ON b.singer_id = s.id
        JOIN users su ON s.user_id = su.id
        WHERE p.id = ? AND b.customer_id = ?
      `, [id, userId]);
    } else if (req.user.role === 'singer') {
      // Singer can only see payments for bookings they were booked for
      const singer = await db.getOne('SELECT id FROM singers WHERE user_id = ?', [userId]);
      
      if (singer) {
        payment = await db.getOne(`
          SELECT 
            p.*, b.booking_date, b.venue, b.event_type, b.status as booking_status,
            cu.id as customer_id, cu.full_name as customer_name,
            s.id as singer_id, su.full_name as singer_name
          FROM payments p
          JOIN bookings b ON p.booking_id = b.id
          JOIN users cu ON b.customer_id = cu.id
          JOIN singers s ON b.singer_id = s.id
          JOIN users su ON s.user_id = su.id
          WHERE p.id = ? AND b.singer_id = ?
        `, [id, singer.id]);
      }
    }
    
    if (!payment) {
      return res.status(404).json({ message: 'Payment not found or you are not authorized to view it' });
    }
    
    res.status(200).json(payment);
  } catch (error) {
    console.error('Get payment details error:', error);
    res.status(500).json({ message: 'Server error while fetching payment details' });
  }
};

import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:taal_connect/models/review.dart';
import 'package:taal_connect/models/singer.dart';
import 'package:taal_connect/models/user.dart';
import 'package:taal_connect/services/http_service.dart';
import 'package:taal_connect/utils/constants.dart';

class SingerService {
  final HttpService _httpService = HttpService();

  // Get user token from shared preferences
  Future<String?> _getToken() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userData = prefs.getString('user');

      if (userData != null) {
        final user = User.fromJson(jsonDecode(userData));
        return user.token;
      }

      return null;
    } catch (e) {
      debugPrint('Error getting token: $e');
      return null;
    }
  }

  // Get featured singers
  Future<List<Singer>> getFeaturedSingers() async {
    try {
      final token = await _getToken();

      final headers = <String, String>{};
      if (token != null) {
        headers['Authorization'] = 'Bearer $token';
      }

      final response = await _httpService.get(
        '${AppConstants.singersUrl}/featured',
        headers: headers.isNotEmpty ? headers : null,
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        return data.map((json) => Singer.fromJson(json)).toList();
      } else {
        throw Exception('Failed to load featured singers');
      }
    } catch (e) {
      debugPrint('Error getting featured singers: $e');
      // Return empty list instead of throwing for better UX
      return [];
    }
  }

  // Get top rated singers
  Future<List<Singer>> getTopRatedSingers() async {
    try {
      final token = await _getToken();

      final headers = <String, String>{};
      if (token != null) {
        headers['Authorization'] = 'Bearer $token';
      }

      final response = await _httpService.get(
        '${AppConstants.singersUrl}/top-rated',
        headers: headers.isNotEmpty ? headers : null,
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        return data.map((json) => Singer.fromJson(json)).toList();
      } else {
        throw Exception('Failed to load top rated singers');
      }
    } catch (e) {
      debugPrint('Error getting top rated singers: $e');
      return [];
    }
  }

  // Get singer by id
  Future<Singer> getSingerById(int id) async {
    try {
      final token = await _getToken();
      if (token == null) {
        throw Exception('Authentication required');
      }

      final response = await _httpService.get(
        '${AppConstants.singersUrl}/$id',
        headers: {
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return Singer.fromJson(data);
      } else {
        throw Exception('Failed to load singer details');
      }
    } catch (e) {
      debugPrint('Error getting singer details: $e');
      rethrow;
    }
  }

  // Search singers
  Future<List<Singer>> searchSingers(String query) async {
    try {
      final token = await _getToken();
      if (token == null) {
        throw Exception('Authentication required');
      }

      final response = await _httpService.get(
        '${AppConstants.singersUrl}/search?q=$query',
        headers: {
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        return data.map((json) => Singer.fromJson(json)).toList();
      } else {
        throw Exception('Failed to search singers');
      }
    } catch (e) {
      debugPrint('Error searching singers: $e');
      return [];
    }
  }

  // Get singers by genre
  Future<List<Singer>> getSingersByGenre(String genre) async {
    try {
      final token = await _getToken();
      if (token == null) {
        throw Exception('Authentication required');
      }

      final response = await _httpService.get(
        '${AppConstants.singersUrl}/genre/$genre',
        headers: {
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        return data.map((json) => Singer.fromJson(json)).toList();
      } else {
        throw Exception('Failed to load singers by genre');
      }
    } catch (e) {
      debugPrint('Error getting singers by genre: $e');
      return [];
    }
  }

  // Get singer reviews
  Future<List<Review>> getSingerReviews(int singerId) async {
    try {
      final token = await _getToken();
      if (token == null) {
        throw Exception('Authentication required');
      }

      final response = await _httpService.get(
        '${AppConstants.singersUrl}/$singerId/reviews',
        headers: {
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        return data.map((json) => Review.fromJson(json)).toList();
      } else {
        throw Exception('Failed to load singer reviews');
      }
    } catch (e) {
      debugPrint('Error getting singer reviews: $e');
      return [];
    }
  }

  // Submit a review
  Future<bool> submitReview({
    required int singerId,
    required int rating,
    required String comment,
  }) async {
    try {
      final token = await _getToken();
      if (token == null) {
        throw Exception('Authentication required');
      }

      final response = await _httpService.post(
        AppConstants.reviewsUrl,
        headers: {
          'Authorization': 'Bearer $token',
        },
        body: jsonEncode({
          'singer_id': singerId,
          'rating': rating,
          'comment': comment,
        }),
      );

      return response.statusCode == 201;
    } catch (e) {
      debugPrint('Error submitting review: $e');
      return false;
    }
  }

  // Get singer availability
  Future<List<String>> getSingerAvailability(int singerId) async {
    try {
      final token = await _getToken();
      if (token == null) {
        throw Exception('Authentication required');
      }

      final response = await _httpService.get(
        '${AppConstants.singersUrl}/$singerId/availability',
        headers: {
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        return data.cast<String>();
      } else {
        throw Exception('Failed to load singer availability');
      }
    } catch (e) {
      debugPrint('Error getting singer availability: $e');
      return [];
    }
  }

  // Get singer availability as a map for the calendar
  Future<Map<DateTime, List<TimeSlot>>> getSingerAvailabilityMap() async {
    try {
      final token = await _getToken();
      if (token == null) {
        throw Exception('Authentication required');
      }

      final response = await _httpService.get(
        '${AppConstants.singersUrl}/my-availability',
        headers: {
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        final Map<String, dynamic> data = jsonDecode(response.body);
        final Map<DateTime, List<TimeSlot>> result = {};

        data.forEach((dateStr, slots) {
          final date = DateTime.parse(dateStr);
          final List<dynamic> slotsData = slots;

          result[date] = slotsData.map((slot) {
            final startParts = slot['start'].split(':');
            final endParts = slot['end'].split(':');

            return TimeSlot(
              start: TimeOfDay(
                hour: int.parse(startParts[0]),
                minute: int.parse(startParts[1]),
              ),
              end: TimeOfDay(
                hour: int.parse(endParts[0]),
                minute: int.parse(endParts[1]),
              ),
            );
          }).toList();
        });

        return result;
      } else {
        throw Exception('Failed to load singer availability');
      }
    } catch (e) {
      debugPrint('Error getting singer availability map: $e');
      return {};
    }
  }

  // Update singer availability
  Future<bool> updateSingerAvailability(
      Map<DateTime, List<TimeSlot>> availability) async {
    try {
      final token = await _getToken();
      if (token == null) {
        throw Exception('Authentication required');
      }

      // Convert the map to the format expected by the API
      final Map<String, List<Map<String, String>>> formattedAvailability = {};

      availability.forEach((date, slots) {
        final dateStr = date.toIso8601String().split('T')[0];

        formattedAvailability[dateStr] = slots.map((slot) {
          return {
            'start':
                '${slot.start.hour.toString().padLeft(2, '0')}:${slot.start.minute.toString().padLeft(2, '0')}',
            'end':
                '${slot.end.hour.toString().padLeft(2, '0')}:${slot.end.minute.toString().padLeft(2, '0')}',
          };
        }).toList();
      });

      final response = await _httpService.put(
        '${AppConstants.singersUrl}/my-availability',
        headers: {
          'Authorization': 'Bearer $token',
        },
        body: jsonEncode(formattedAvailability),
      );

      return response.statusCode == 200;
    } catch (e) {
      debugPrint('Error updating singer availability: $e');
      return false;
    }
  }

  // Get available genres
  Future<List<String>> getCategories() async {
    try {
      final token = await _getToken();

      final headers = <String, String>{};
      if (token != null) {
        headers['Authorization'] = 'Bearer $token';
      }

      final response = await _httpService.get(
        '${AppConstants.singersUrl}/genres',
        headers: headers.isNotEmpty ? headers : null,
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        return data.cast<String>();
      } else {
        throw Exception('Failed to load genres');
      }
    } catch (e) {
      debugPrint('Error getting genres: $e');
      return [
        'Classical',
        'Folk',
        'Pop',
        'Rock',
        'Sufi',
        'Ghazal'
      ]; // Return default genres for better UX
    }
  }
}

class TimeSlot {
  final TimeOfDay start;
  final TimeOfDay end;

  TimeSlot({required this.start, required this.end});
}

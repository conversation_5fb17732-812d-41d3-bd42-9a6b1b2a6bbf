import 'package:flutter/material.dart';
import 'package:pin_code_fields/pin_code_fields.dart';

class ForgetPassPagetwo extends StatefulWidget {
  const ForgetPassPagetwo({super.key});

  @override
  State<ForgetPassPagetwo> createState() => _ForgetPassPagetwoState();
}

class _ForgetPassPagetwoState extends State<ForgetPassPagetwo> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xffF2EFDF),
      appBar: AppBar(
        backgroundColor: const Color(0xffF2EFDF),
        title: const Text('back'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          children: [
            const Text(
              'Forget\nPassword?',
              style: TextStyle(
                fontSize: 50,
              ),
            ),
            const SizedBox(
              height: 20,
            ),
            Container(
              height: 400,
              decoration: BoxDecoration(
                color: const Color(0xffCFD9BA),
                borderRadius: BorderRadius.circular(20),
              ),
            ),
            const SizedBox(
              height: 30,
            ),
            ElevatedButton(
                onPressed: () {
                  Navigator.push(
                      context,
                      MaterialPageRoute(
                          builder: (context) => const ForgetPassPagetwo()));
                },
                style: ElevatedButton.styleFrom(
                  minimumSize: const Size(220, 36),
                  backgroundColor: const Color(0xff425951), // Button color
                  foregroundColor: const Color(0xffFFFFFF), // Text color
                  padding:
                      const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                  elevation: 5, // Shadow effect
                ),
                child: const Text('Continue')),
          ],
        ),
      ),
    );
  }
}

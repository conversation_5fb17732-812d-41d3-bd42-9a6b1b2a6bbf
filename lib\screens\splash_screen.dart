import 'dart:async';
import 'package:flutter/material.dart';
import 'package:taal_connect/screens/auth/login_screen.dart';
import 'package:taal_connect/services/auth_service.dart';
import 'package:taal_connect/utils/theme.dart';
import 'package:provider/provider.dart';
import 'package:taal_connect/screens/customer/customer_dashboard.dart';
import 'package:taal_connect/screens/singer/singer_dashboard.dart';
import 'package:taal_connect/utils/constants.dart';
import 'package:taal_connect/widgets/logo.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({Key? key}) : super(key: key);

  @override
  _SplashScreenState createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  void initState() {
    super.initState();
    Timer(const Duration(seconds: 2), () {
      checkUserStatus();
    });
  }

  void checkUserStatus() async {
    final authService = Provider.of<AuthService>(context, listen: false);
    final user = await authService.getCurrentUser();

    if (user != null) {
      // User is logged in, navigate to appropriate dashboard
      if (user.role == AppConstants.roleCustomer) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (_) => const CustomerDashboard()),
        );
      } else if (user.role == AppConstants.roleSinger) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (_) => const SingerDashboard()),
        );
      } else {
        // Default to login if role is not recognized
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (_) => const LoginScreen()),
        );
      }
    } else {
      // User is not logged in, navigate to login screen
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(builder: (_) => const LoginScreen()),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        width: double.infinity,
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppColors.primaryDark,
              AppColors.accentGreen,
            ],
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Logo(size: 120, color: AppColors.primaryLight),
            const SizedBox(height: 24),
            Text(
              'TaalConnect',
              style: Theme.of(context).textTheme.displayMedium?.copyWith(
                    color: AppColors.primaryLight,
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 48),
            const CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(AppColors.primaryLight),
            ),
          ],
        ),
      ),
    );
  }
}

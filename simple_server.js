const http = require('http');
const url = require('url');

const server = http.createServer((req, res) => {
  // Enable CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  res.setHeader('Content-Type', 'application/json');
  
  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }
  
  const parsedUrl = url.parse(req.url, true);
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
  
  if (parsedUrl.pathname === '/health') {
    res.writeHead(200);
    res.end(JSON.stringify({ status: 'OK', message: 'Simple server running' }));
    return;
  }
  
  if (parsedUrl.pathname === '/api/auth/login') {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });
    
    req.on('end', () => {
      console.log('Login request body:', body);
      res.writeHead(200);
      res.end(JSON.stringify({ 
        success: true, 
        message: 'Login successful',
        token: 'test-token-123',
        user: { id: 1, email: '<EMAIL>', name: 'Test User' }
      }));
    });
    return;
  }
  
  // Default response
  res.writeHead(404);
  res.end(JSON.stringify({ error: 'Not found' }));
});

const PORT = 8002;
server.listen(PORT, '0.0.0.0', () => {
  console.log(`Simple server running on port ${PORT}`);
  console.log(`Test with: http://localhost:${PORT}/health`);
});

import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';

class HttpService {
  static final HttpService _instance = HttpService._internal();
  factory HttpService() => _instance;
  HttpService._internal();

  late http.Client _client;

  // List of base URLs to try in order
  static const List<String> _baseUrls = [
    'http://localhost:8000/api',
    'http://***********:8000/api',
    'http://************:8000/api',
  ];

  void init() {
    _client = http.Client();
  }

  // Helper method to convert relative URL to absolute URL with fallback
  String _getFullUrl(String url) {
    if (url.startsWith('http')) {
      return url; // Already absolute URL
    }

    // For relative URLs, use the primary base URL
    return '${_baseUrls[0]}${url.startsWith('/') ? url : '/$url'}';
  }

  // Helper method to try multiple base URLs for a given endpoint
  Future<http.Response> _tryMultipleUrls(
    String endpoint,
    Future<http.Response> Function(String url) requestFunction,
  ) async {
    Exception? lastException;

    // If endpoint is already a full URL, just try it once
    if (endpoint.startsWith('http')) {
      try {
        debugPrint('Using provided full URL: $endpoint');
        return await requestFunction(endpoint);
      } catch (e) {
        debugPrint('Failed with provided URL: $endpoint - Error: $e');
        throw e is Exception ? e : Exception(e.toString());
      }
    }

    // Try each base URL with the endpoint
    for (String baseUrl in _baseUrls) {
      try {
        // Extract the endpoint path from the full URL if needed
        String endpointPath = endpoint;
        if (endpoint.contains('/api/')) {
          endpointPath = endpoint.substring(endpoint.indexOf('/api/') + 4);
        }

        final fullUrl = '$baseUrl/$endpointPath';
        debugPrint('Trying URL: $fullUrl');
        final response = await requestFunction(fullUrl);
        debugPrint('Success with URL: $fullUrl');
        return response;
      } catch (e) {
        debugPrint('Failed with base URL: $baseUrl - Error: $e');
        lastException = e is Exception ? e : Exception(e.toString());
        continue;
      }
    }

    // If all URLs failed, throw the last exception
    throw lastException ?? Exception('All connection attempts failed');
  }

  Future<http.Response> post(
    String url, {
    Map<String, String>? headers,
    Object? body,
    Duration timeout = const Duration(seconds: 30),
  }) async {
    return await _tryMultipleUrls(url, (String fullUrl) async {
      try {
        debugPrint('Making POST request to: $fullUrl');
        debugPrint('Headers: $headers');
        debugPrint('Body: $body');

        final uri = Uri.parse(fullUrl);

        // Create custom headers with proper connection handling
        final customHeaders = {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Connection':
              'keep-alive', // Changed from 'close' to 'keep-alive' for better performance
          'User-Agent': 'TaalConnect-Flutter-App',
          ...?headers,
        };

        final response = await _client
            .post(
              uri,
              headers: customHeaders,
              body: body,
            )
            .timeout(timeout);

        debugPrint('Response status: ${response.statusCode}');
        debugPrint('Response headers: ${response.headers}');
        debugPrint('Response body: ${response.body}');

        return response;
      } on SocketException catch (e) {
        debugPrint('SocketException: $e');
        throw Exception(
            'Network error: Unable to connect to server - ${e.message}');
      } on HttpException catch (e) {
        debugPrint('HttpException: $e');
        throw Exception('HTTP error: ${e.message}');
      } on TimeoutException catch (e) {
        debugPrint('TimeoutException: $e');
        throw Exception('Request timeout: Server took too long to respond');
      } on FormatException catch (e) {
        debugPrint('FormatException: $e');
        throw Exception('Invalid response format');
      } catch (e) {
        debugPrint('Unexpected error: $e');
        throw Exception('Connection error: $e');
      }
    });
  }

  Future<http.Response> get(
    String url, {
    Map<String, String>? headers,
    Duration timeout = const Duration(seconds: 30),
  }) async {
    return await _tryMultipleUrls(url, (String fullUrl) async {
      try {
        debugPrint('Making GET request to: $fullUrl');
        debugPrint('Headers: $headers');

        final uri = Uri.parse(fullUrl);

        // Create custom headers with proper connection handling
        final customHeaders = {
          'Accept': 'application/json',
          'Connection':
              'keep-alive', // Changed from 'close' to 'keep-alive' for better performance
          'User-Agent': 'TaalConnect-Flutter-App',
          ...?headers,
        };

        final response = await _client
            .get(
              uri,
              headers: customHeaders,
            )
            .timeout(timeout);

        debugPrint('Response status: ${response.statusCode}');
        debugPrint('Response headers: ${response.headers}');
        debugPrint('Response body: ${response.body}');

        return response;
      } on SocketException catch (e) {
        debugPrint('SocketException: $e');
        throw Exception(
            'Network error: Unable to connect to server - ${e.message}');
      } on HttpException catch (e) {
        debugPrint('HttpException: $e');
        throw Exception('HTTP error: ${e.message}');
      } on TimeoutException catch (e) {
        debugPrint('TimeoutException: $e');
        throw Exception('Request timeout: Server took too long to respond');
      } on FormatException catch (e) {
        debugPrint('FormatException: $e');
        throw Exception('Invalid response format');
      } catch (e) {
        debugPrint('Unexpected error: $e');
        throw Exception('Connection error: $e');
      }
    });
  }

  Future<http.Response> put(
    String url, {
    Map<String, String>? headers,
    Object? body,
    Duration timeout = const Duration(seconds: 30),
  }) async {
    return await _tryMultipleUrls(url, (String fullUrl) async {
      try {
        debugPrint('Making PUT request to: $fullUrl');
        debugPrint('Headers: $headers');
        debugPrint('Body: $body');

        final uri = Uri.parse(fullUrl);

        // Create custom headers with proper connection handling
        final customHeaders = {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Connection':
              'keep-alive', // Changed from 'close' to 'keep-alive' for better performance
          'User-Agent': 'TaalConnect-Flutter-App',
          ...?headers,
        };

        final response = await _client
            .put(
              uri,
              headers: customHeaders,
              body: body,
            )
            .timeout(timeout);

        debugPrint('Response status: ${response.statusCode}');
        debugPrint('Response headers: ${response.headers}');
        debugPrint('Response body: ${response.body}');

        return response;
      } on SocketException catch (e) {
        debugPrint('SocketException: $e');
        throw Exception(
            'Network error: Unable to connect to server - ${e.message}');
      } on HttpException catch (e) {
        debugPrint('HttpException: $e');
        throw Exception('HTTP error: ${e.message}');
      } on TimeoutException catch (e) {
        debugPrint('TimeoutException: $e');
        throw Exception('Request timeout: Server took too long to respond');
      } on FormatException catch (e) {
        debugPrint('FormatException: $e');
        throw Exception('Invalid response format');
      } catch (e) {
        debugPrint('Unexpected error: $e');
        throw Exception('Connection error: $e');
      }
    });
  }

  void dispose() {
    _client.close();
  }
}

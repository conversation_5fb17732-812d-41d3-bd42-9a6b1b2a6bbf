import 'package:flutter_test/flutter_test.dart';
import 'package:taal_connect/services/http_service.dart';

void main() {
  group('HttpService Tests', () {
    late HttpService httpService;

    setUp(() {
      httpService = HttpService();
      httpService.init();
    });

    tearDown(() {
      httpService.dispose();
    });

    test('should connect to server health endpoint', () async {
      try {
        final response = await httpService.get('http://localhost:8000/health');
        expect(response.statusCode, equals(200));
        print('Health check successful: ${response.body}');
      } catch (e) {
        fail('Failed to connect to server: $e');
      }
    });

    test('should handle login endpoint connection', () async {
      try {
        final response = await httpService.post(
          'http://localhost:8000/api/auth/login',
          body: '{"email":"<EMAIL>","password":"test123"}',
        );

        // We expect either 401 (invalid credentials) or 200 (success)
        // Both indicate the server is reachable and processing requests
        expect(response.statusCode, isIn([200, 401]));
        print(
            'Login endpoint reachable: ${response.statusCode} - ${response.body}');
      } catch (e) {
        fail('Failed to connect to login endpoint: $e');
      }
    });

    test('should retry with multiple URLs on connection failure', () async {
      try {
        // This should fail with the first URL but succeed with localhost
        final response =
            await httpService.get('http://192.168.999.999:8000/health');
        expect(response.statusCode, equals(200));
        print('Retry mechanism working: ${response.body}');
      } catch (e) {
        // If all URLs fail, we should get a meaningful error message
        expect(e.toString(), contains('connection attempts failed'));
        print('Expected failure with meaningful error: $e');
      }
    });

    test('should handle PUT requests for role updates', () async {
      try {
        // Test PUT method with role update endpoint
        final response = await httpService.put(
          'http://localhost:8000/api/auth/update-role',
          headers: {
            'Authorization': 'Bearer invalid_token_for_test',
          },
          body: '{"role":"customer"}',
        );

        // We expect 401 (unauthorized) since we're using an invalid token
        // This confirms the endpoint is reachable and processing PUT requests
        expect(response.statusCode, equals(401));
        print(
            'PUT endpoint reachable: ${response.statusCode} - ${response.body}');
      } catch (e) {
        fail('Failed to connect to role update endpoint: $e');
      }
    });
  });
}

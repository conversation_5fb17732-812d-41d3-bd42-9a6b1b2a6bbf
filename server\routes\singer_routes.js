const express = require('express');
const router = express.Router();
const singerController = require('../controllers/singer_controller');
const authMiddleware = require('../middleware/auth_middleware');

// Routes that don't require authentication
router.get('/genres', singerController.getGenres);
router.get('/featured', singerController.getFeaturedSingers);
router.get('/top-rated', singerController.getTopRatedSingers);

// Routes that require authentication
router.use(authMiddleware.verifyToken);

// Search singers
router.get('/search', singerController.searchSingers);

// Get singers by genre
router.get('/genre/:genreName', singerController.getSingersByGenre);

// Get singer by ID
router.get('/:id', singerController.getSingerById);

// Get singer reviews
router.get('/:id/reviews', singerController.getSingerReviews);

// Get singer availability
router.get('/:id/availability', singerController.getSingerAvailability);

// Check singer availability for a specific time
router.get('/:id/check-availability', singerController.checkSingerAvailability);

// Get my availability (for singers only)
router.get('/my-availability', authMiddleware.isSinger, singerController.getMyAvailability);

// Update my availability (for singers only)
router.put('/my-availability', authMiddleware.isSinger, singerController.updateMyAvailability);

module.exports = router;

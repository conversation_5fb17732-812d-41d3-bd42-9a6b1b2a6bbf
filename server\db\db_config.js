const { Pool } = require('pg');
const dotenv = require('dotenv');
const path = require('path');

dotenv.config({ path: path.join(__dirname, '../../.env') });

// Debug: Log connection parameters (remove in production)
console.log('DB Connection params:', {
  host: process.env.DB_HOST,
  port: process.env.DB_PORT,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD ? '***' : 'undefined',
  database: process.env.DB_NAME
});

// Create connection pool with PostgreSQL
const pool = new Pool({
  host: process.env.DB_HOST,
  port: parseInt(process.env.DB_PORT),
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
});

// Create a wrapper for the pool to provide a similar interface as before
const promisePool = {
  async execute(sql, params = []) {
    // Convert MySQL placeholders (?) to PostgreSQL placeholders ($1, $2, etc.)
    let pgSql = sql;
    if (params.length > 0) {
      let paramCount = 0;
      pgSql = sql.replace(/\?/g, () => `$${++paramCount}`);
    }
    return pool.query(pgSql, params);
  },
  async query(sql, params = []) {
    // Convert MySQL placeholders (?) to PostgreSQL placeholders ($1, $2, etc.)
    let pgSql = sql;
    if (params.length > 0) {
      let paramCount = 0;
      pgSql = sql.replace(/\?/g, () => `$${++paramCount}`);
    }
    return pool.query(pgSql, params);
  },
  async getConnection() {
    const client = await pool.connect();
    return {
      execute: async (sql, params = []) => {
        // Convert MySQL placeholders (?) to PostgreSQL placeholders ($1, $2, etc.)
        let pgSql = sql;
        if (params.length > 0) {
          let paramCount = 0;
          pgSql = sql.replace(/\?/g, () => `$${++paramCount}`);
        }
        return client.query(pgSql, params);
      },
      query: async (sql, params = []) => {
        // Convert MySQL placeholders (?) to PostgreSQL placeholders ($1, $2, etc.)
        let pgSql = sql;
        if (params.length > 0) {
          let paramCount = 0;
          pgSql = sql.replace(/\?/g, () => `$${++paramCount}`);
        }
        return client.query(pgSql, params);
      },
      beginTransaction: async () => client.query('BEGIN'),
      commit: async () => client.query('COMMIT'),
      rollback: async () => client.query('ROLLBACK'),
      release: () => client.release()
    };
  }
};

// Test connection
const testConnection = async () => {
  try {
    const result = await promisePool.query('SELECT 1');
    console.log('Database connection successful!');
    return true;
  } catch (error) {
    console.error('Database connection failed:', error);
    return false;
  }
};

// Execute query with parameters
const executeQuery = async (sql, params = []) => {
  try {
    const result = await promisePool.execute(sql, params);
    return result.rows;
  } catch (error) {
    console.error('Error executing query:', error);
    throw error;
  }
};

// Get a single record
const getOne = async (sql, params = []) => {
  try {
    const result = await promisePool.execute(sql, params);
    return result.rows.length > 0 ? result.rows[0] : null;
  } catch (error) {
    console.error('Error executing query:', error);
    throw error;
  }
};

// Insert a record and return the inserted ID
const insertAndGetId = async (sql, params = []) => {
  try {
    // Modify the query to return the inserted ID explicitly for PostgreSQL
    // This assumes the table has an id column that's auto-incremented
    let pgSql = sql;
    if (pgSql.toLowerCase().includes('insert into')) {
      pgSql += " RETURNING id";
    }

    let paramCount = 0;
    pgSql = pgSql.replace(/\?/g, () => `$${++paramCount}`);

    const result = await pool.query(pgSql, params);
    return result.rows.length > 0 ? result.rows[0].id : null;
  } catch (error) {
    console.error('Error inserting record:', error);
    throw error;
  }
};

// Begin a transaction
const beginTransaction = async () => {
  const connection = await promisePool.getConnection();
  await connection.beginTransaction();
  return connection;
};

// Commit a transaction
const commitTransaction = async (connection) => {
  await connection.commit();
  connection.release();
};

// Rollback a transaction
const rollbackTransaction = async (connection) => {
  await connection.rollback();
  connection.release();
};

module.exports = {
  pool: promisePool,
  testConnection,
  executeQuery,
  getOne,
  insertAndGetId,
  beginTransaction,
  commitTransaction,
  rollbackTransaction
};

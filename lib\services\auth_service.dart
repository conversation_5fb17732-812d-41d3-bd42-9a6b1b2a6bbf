import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:taal_connect/models/user.dart';
import 'package:taal_connect/utils/constants.dart';
import 'package:taal_connect/services/http_service.dart';

class AuthService extends ChangeNotifier {
  User? _currentUser;
  bool _isLoading = false;
  final HttpService _httpService = HttpService();

  User? get currentUser => _currentUser;
  bool get isLoading => _isLoading;
  bool get isAuthenticated => _currentUser != null;

  // Initialize service and load user from SharedPreferences if available
  Future<void> init() async {
    _isLoading = true;
    notifyListeners();

    try {
      final user = await getCurrentUser();
      _currentUser = user;
    } catch (e) {
      debugPrint('Error initializing auth service: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Get the current user from shared preferences
  Future<User?> getCurrentUser() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userData = prefs.getString('user');

      if (userData != null) {
        final user = User.fromJson(jsonDecode(userData));

        // Check if token is valid by making a request to the server
        final response = await http.get(
          Uri.parse(AppConstants.userProfileUrl),
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer ${user.token}',
          },
        );

        if (response.statusCode == 200) {
          _currentUser = user;
          return user;
        } else {
          // Token is invalid, clear shared preferences
          await prefs.remove('user');
          _currentUser = null;
          return null;
        }
      }

      return null;
    } catch (e) {
      debugPrint('Error getting current user: $e');
      return null;
    }
  }

  // Login user with email and password
  Future<User?> login(String email, String password, bool rememberMe) async {
    _isLoading = true;
    notifyListeners();

    try {
      final response = await _httpService.post(
        AppConstants.loginUrl,
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'email': email,
          'password': password,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final user = User.fromJson(data);

        if (rememberMe) {
          // Save user data to shared preferences
          final prefs = await SharedPreferences.getInstance();
          await prefs.setString('user', jsonEncode(user.toJson()));
        }

        _currentUser = user;
        notifyListeners();
        return user;
      } else {
        final error = jsonDecode(response.body)['message'] ?? 'Login failed';
        throw Exception(error);
      }
    } catch (e) {
      debugPrint('Error logging in: $e');
      rethrow;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Register a new user
  Future<User?> register(
    String fullName,
    String email,
    String password,
    String cnicNumber,
    String phoneNumber,
  ) async {
    _isLoading = true;
    notifyListeners();

    try {
      final response = await _httpService.post(
        AppConstants.signupUrl,
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'full_name': fullName,
          'email': email,
          'password': password,
          'cnic_number': cnicNumber,
          'phone_number': phoneNumber,
        }),
      );

      if (response.statusCode == 201) {
        final data = jsonDecode(response.body);
        final user = User.fromJson(data);

        // Save user data to shared preferences
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('user', jsonEncode(user.toJson()));

        _currentUser = user;
        notifyListeners();
        return user;
      } else {
        final error =
            jsonDecode(response.body)['message'] ?? 'Registration failed';
        throw Exception(error);
      }
    } catch (e) {
      debugPrint('Error registering: $e');
      rethrow;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Login with Google
  Future<User?> loginWithGoogle() async {
    _isLoading = true;
    notifyListeners();

    try {
      // This would normally connect to the Google Sign-In API
      // For now, we'll mock a successful response

      // In a real implementation, add Google Sign In package and authenticate with Google
      // final googleUser = await GoogleSignIn().signIn();
      // final googleAuth = await googleUser?.authentication;
      // final token = googleAuth?.idToken;

      // Then send the token to your backend
      final response = await http.post(
        Uri.parse('${AppConstants.baseUrl}/auth/google'),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'token': 'google-token-placeholder',
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final user = User.fromJson(data);

        // Save user data to shared preferences
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('user', jsonEncode(user.toJson()));

        _currentUser = user;
        notifyListeners();
        return user;
      } else {
        throw Exception('Google login failed');
      }
    } catch (e) {
      debugPrint('Error logging in with Google: $e');
      rethrow;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Update user role
  Future<bool> updateUserRole(String role) async {
    if (_currentUser == null) return false;

    _isLoading = true;
    notifyListeners();

    try {
      final response = await http.put(
        Uri.parse('${AppConstants.userProfileUrl}/role'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ${_currentUser!.token}',
        },
        body: jsonEncode({
          'role': role,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final updatedUser = User.fromJson(data);

        // Update user data in shared preferences
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('user', jsonEncode(updatedUser.toJson()));

        _currentUser = updatedUser;
        notifyListeners();
        return true;
      } else {
        throw Exception('Failed to update user role');
      }
    } catch (e) {
      debugPrint('Error updating user role: $e');
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Logout user
  Future<void> logout() async {
    _isLoading = true;
    notifyListeners();

    try {
      // Clear saved user data
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('user');

      _currentUser = null;
      notifyListeners();
    } catch (e) {
      debugPrint('Error logging out: $e');
      rethrow;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Request password reset
  Future<bool> requestPasswordReset(String email) async {
    _isLoading = true;
    notifyListeners();

    try {
      final response = await http.post(
        Uri.parse(AppConstants.forgotPasswordUrl),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'email': email,
        }),
      );

      final success = response.statusCode == 200;
      return success;
    } catch (e) {
      debugPrint('Error requesting password reset: $e');
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Verify reset code
  Future<bool> verifyResetCode(String email, String code) async {
    _isLoading = true;
    notifyListeners();

    try {
      final response = await http.post(
        Uri.parse(AppConstants.verifyCodeUrl),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'email': email,
          'code': code,
        }),
      );

      final success = response.statusCode == 200;
      return success;
    } catch (e) {
      debugPrint('Error verifying reset code: $e');
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Reset password
  Future<bool> resetPassword(
      String email, String code, String newPassword) async {
    _isLoading = true;
    notifyListeners();

    try {
      final response = await http.post(
        Uri.parse(AppConstants.resetPasswordUrl),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'email': email,
          'code': code,
          'new_password': newPassword,
        }),
      );

      final success = response.statusCode == 200;
      return success;
    } catch (e) {
      debugPrint('Error resetting password: $e');
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }
}

const db = require('./db/db_config');
const bcrypt = require('bcrypt');

async function addMoreSingers() {
  try {
    console.log('Adding more singers...');

    const moreSingers = [
      {
        full_name: '<PERSON><PERSON>',
        email: '<EMAIL>',
        password: 'singer123',
        phone_number: '+923001111111',
        cnic_number: '42101-1111111-1',
        description: 'World-renowned Qawwali and Sufi singer. Son of legendary Ustad <PERSON><PERSON><PERSON>. Perfect for spiritual and cultural events.',
        hourly_rate: 15000.00,
        rating: 5.0,
        review_count: 50,
        genres: ['Qawwali', 'Sufi', 'Classical'],
        is_featured: true
      },
      {
        full_name: '<PERSON><PERSON>',
        email: '<EMAIL>',
        password: 'singer123',
        phone_number: '+923002222222',
        cnic_number: '42101-2222222-2',
        description: 'Popular Pakistani pop and rock singer. Known for melodious voice and contemporary hits. Great for modern events and concerts.',
        hourly_rate: 12000.00,
        rating: 4.9,
        review_count: 45,
        genres: ['Pop', 'Rock', 'Folk'],
        is_featured: true
      },
      {
        full_name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
        email: '<EMAIL>',
        password: 'singer123',
        phone_number: '+923003333333',
        cnic_number: '42101-3333333-3',
        description: 'Classical and semi-classical singer with expertise in ghazals and thumri. Brings traditional elegance to every performance.',
        hourly_rate: 8000.00,
        rating: 4.8,
        review_count: 35,
        genres: ['Classical', 'Ghazal', 'Folk'],
        is_featured: true
      },
      {
        full_name: 'Momina Mustehsan',
        email: '<EMAIL>',
        password: 'singer123',
        phone_number: '+923004444444',
        cnic_number: '42101-4444444-4',
        description: 'Contemporary female vocalist known for fusion music. Perfect blend of traditional and modern styles for diverse audiences.',
        hourly_rate: 7000.00,
        rating: 4.7,
        review_count: 30,
        genres: ['Pop', 'Folk', 'Jazz'],
        is_featured: true
      },
      {
        full_name: 'Ali Sethi',
        email: '<EMAIL>',
        password: 'singer123',
        phone_number: '+923005555555',
        cnic_number: '42101-5555555-5',
        description: 'Versatile singer specializing in ghazals and contemporary music. Known for soulful renditions and artistic performances.',
        hourly_rate: 9000.00,
        rating: 4.8,
        review_count: 28,
        genres: ['Ghazal', 'Classical', 'Pop'],
        is_featured: false
      },
      {
        full_name: 'Sanam Marvi',
        email: '<EMAIL>',
        password: 'singer123',
        phone_number: '+923006666666',
        cnic_number: '42101-6666666-6',
        description: 'Folk and Sufi singer specializing in traditional Sindhi and Punjabi music. Authentic cultural performances.',
        hourly_rate: 6500.00,
        rating: 4.6,
        review_count: 25,
        genres: ['Folk', 'Sufi', 'Classical'],
        is_featured: false
      },
      {
        full_name: 'Asim Azhar',
        email: '<EMAIL>',
        password: 'singer123',
        phone_number: '+923007777777',
        cnic_number: '42101-7777777-7',
        description: 'Young and energetic pop singer. Perfect for youth events, parties, and modern celebrations.',
        hourly_rate: 5500.00,
        rating: 4.5,
        review_count: 22,
        genres: ['Pop', 'Rock', 'Jazz'],
        is_featured: false
      },
      {
        full_name: 'Quratulain Balouch',
        email: '<EMAIL>',
        password: 'singer123',
        phone_number: '+923008888888',
        cnic_number: '42101-8888888-8',
        description: 'Indie and alternative rock singer with unique voice. Great for artistic and contemporary events.',
        hourly_rate: 6000.00,
        rating: 4.7,
        review_count: 20,
        genres: ['Rock', 'Pop', 'Jazz'],
        is_featured: false
      },
      {
        full_name: 'Farhan Saeed',
        email: '<EMAIL>',
        password: 'singer123',
        phone_number: '+923009999999',
        cnic_number: '42101-9999999-9',
        description: 'Pop and rock singer, former lead vocalist of Jal band. Excellent for concerts and live performances.',
        hourly_rate: 7500.00,
        rating: 4.6,
        review_count: 18,
        genres: ['Pop', 'Rock', 'Folk'],
        is_featured: false
      },
      {
        full_name: 'Hadiqa Kiani',
        email: '<EMAIL>',
        password: 'singer123',
        phone_number: '+923001010101',
        cnic_number: '42101-1010101-0',
        description: 'Veteran pop singer with decades of experience. Known for powerful vocals and stage presence.',
        hourly_rate: 10000.00,
        rating: 4.9,
        review_count: 40,
        genres: ['Pop', 'Folk', 'Classical'],
        is_featured: true
      }
    ];

    for (const singerData of moreSingers) {
      // Check if user already exists
      const existingUser = await db.getOne('SELECT id FROM users WHERE email = ?', [singerData.email]);
      
      if (!existingUser) {
        // Hash password
        const hashedPassword = await bcrypt.hash(singerData.password, 10);
        
        // Create user
        const userId = await db.insertAndGetId(
          'INSERT INTO users (full_name, email, password, phone_number, cnic_number, role) VALUES (?, ?, ?, ?, ?, ?)',
          [singerData.full_name, singerData.email, hashedPassword, singerData.phone_number, singerData.cnic_number, 'singer']
        );

        // Create singer profile
        const singerId = await db.insertAndGetId(
          'INSERT INTO singers (user_id, description, hourly_rate, rating, review_count, is_featured) VALUES (?, ?, ?, ?, ?, ?)',
          [userId, singerData.description, singerData.hourly_rate, singerData.rating, singerData.review_count, singerData.is_featured || false]
        );

        // Add genres for this singer
        for (const genreName of singerData.genres) {
          const genre = await db.getOne('SELECT id FROM genres WHERE name = ?', [genreName]);
          if (genre) {
            await db.executeQuery(
              'INSERT INTO singer_genres (singer_id, genre_id) VALUES (?, ?)',
              [singerId, genre.id]
            );
          }
        }

        console.log(`Added singer: ${singerData.full_name} (Featured: ${singerData.is_featured || false})`);
      } else {
        console.log(`Singer already exists: ${singerData.full_name}`);
      }
    }

    // Update existing singers to be featured
    await db.executeQuery('UPDATE singers SET is_featured = true WHERE id IN (SELECT id FROM singers ORDER BY rating DESC LIMIT 5)');
    
    console.log('Updated existing singers to be featured');
    console.log('More singers added successfully!');
    
    // Show summary
    const totalSingers = await db.getOne('SELECT COUNT(*) as count FROM singers');
    const featuredSingers = await db.getOne('SELECT COUNT(*) as count FROM singers WHERE is_featured = true');
    
    console.log(`Total singers: ${totalSingers.count}`);
    console.log(`Featured singers: ${featuredSingers.count}`);
    
  } catch (error) {
    console.error('Error adding more singers:', error);
  } finally {
    process.exit(0);
  }
}

addMoreSingers();

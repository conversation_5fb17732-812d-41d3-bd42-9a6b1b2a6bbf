const db = require('./db/db_config');
const bcrypt = require('bcrypt');

async function addSampleData() {
  try {
    console.log('Adding sample data...');

    // First, let's add some genres if they don't exist
    const genres = ['Classical', 'Folk', 'Pop', 'Rock', 'Sufi', 'Ghazal', 'Qawwali', 'Jazz'];
    
    for (const genreName of genres) {
      const existingGenre = await db.getOne('SELECT id FROM genres WHERE name = ?', [genreName]);
      if (!existingGenre) {
        await db.executeQuery('INSERT INTO genres (name) VALUES (?)', [genreName]);
        console.log(`Added genre: ${genreName}`);
      }
    }

    // Add sample singers
    const sampleSingers = [
      {
        full_name: '<PERSON><PERSON>',
        email: '<EMAIL>',
        password: 'singer123',
        phone_number: '+923001234567',
        cnic_number: '42101-1234567-8',
        description: 'Professional classical and folk singer with 10+ years of experience. Specializes in traditional Pakistani music and modern fusion.',
        hourly_rate: 5000.00,
        rating: 4.8,
        review_count: 25,
        genres: ['Classical', 'Folk', 'Sufi']
      },
      {
        full_name: '<PERSON> <PERSON>',
        email: '<EMAIL>',
        password: 'singer123',
        phone_number: '+923009876543',
        cnic_number: '42101-9876543-2',
        description: 'Contemporary pop and rock singer. Perfect for modern events, parties, and concerts. Young and energetic performer.',
        hourly_rate: 4500.00,
        rating: 4.6,
        review_count: 18,
        genres: ['Pop', 'Rock', 'Jazz']
      },
      {
        full_name: 'Fatima Noor',
        email: '<EMAIL>',
        password: 'singer123',
        phone_number: '+923007654321',
        cnic_number: '42101-7654321-5',
        description: 'Sufi and Qawwali specialist. Brings spiritual depth to every performance. Ideal for cultural and religious events.',
        hourly_rate: 6000.00,
        rating: 4.9,
        review_count: 32,
        genres: ['Sufi', 'Qawwali', 'Classical']
      },
      {
        full_name: 'Hassan Malik',
        email: '<EMAIL>',
        password: 'singer123',
        phone_number: '+923005432109',
        cnic_number: '42101-5432109-7',
        description: 'Ghazal and classical music expert. Trained in traditional techniques with a modern touch. Perfect for intimate gatherings.',
        hourly_rate: 5500.00,
        rating: 4.7,
        review_count: 22,
        genres: ['Ghazal', 'Classical', 'Folk']
      },
      {
        full_name: 'Zara Sheikh',
        email: '<EMAIL>',
        password: 'singer123',
        phone_number: '+923002109876',
        cnic_number: '42101-2109876-3',
        description: 'Versatile performer covering multiple genres. Great for weddings, corporate events, and private parties.',
        hourly_rate: 4000.00,
        rating: 4.5,
        review_count: 15,
        genres: ['Pop', 'Folk', 'Classical']
      }
    ];

    for (const singerData of sampleSingers) {
      // Check if user already exists
      const existingUser = await db.getOne('SELECT id FROM users WHERE email = ?', [singerData.email]);
      
      if (!existingUser) {
        // Hash password
        const hashedPassword = await bcrypt.hash(singerData.password, 10);
        
        // Create user
        const userId = await db.insertAndGetId(
          'INSERT INTO users (full_name, email, password, phone_number, cnic_number, role) VALUES (?, ?, ?, ?, ?, ?)',
          [singerData.full_name, singerData.email, hashedPassword, singerData.phone_number, singerData.cnic_number, 'singer']
        );

        // Create singer profile
        const singerId = await db.insertAndGetId(
          'INSERT INTO singers (user_id, description, hourly_rate, rating, review_count) VALUES (?, ?, ?, ?, ?)',
          [userId, singerData.description, singerData.hourly_rate, singerData.rating, singerData.review_count]
        );

        // Add genres for this singer
        for (const genreName of singerData.genres) {
          const genre = await db.getOne('SELECT id FROM genres WHERE name = ?', [genreName]);
          if (genre) {
            await db.executeQuery(
              'INSERT INTO singer_genres (singer_id, genre_id) VALUES (?, ?)',
              [singerId, genre.id]
            );
          }
        }

        console.log(`Added singer: ${singerData.full_name}`);
      } else {
        console.log(`Singer already exists: ${singerData.full_name}`);
      }
    }

    console.log('Sample data added successfully!');
  } catch (error) {
    console.error('Error adding sample data:', error);
  } finally {
    process.exit(0);
  }
}

addSampleData();

const db = require('./db/db_config');

async function updateSchema() {
  try {
    console.log('Updating database schema...');
    
    // Add is_featured column to singers table
    await db.executeQuery('ALTER TABLE singers ADD COLUMN IF NOT EXISTS is_featured BOOLEAN DEFAULT false');
    console.log('✅ Added is_featured column to singers table');
    
    // Show current table structure
    const columns = await db.executeQuery(`
      SELECT column_name, data_type, is_nullable 
      FROM information_schema.columns 
      WHERE table_name = 'singers' 
      ORDER BY ordinal_position
    `);
    
    console.log('\nCurrent singers table structure:');
    columns.forEach(col => {
      console.log(`- ${col.column_name}: ${col.data_type} (${col.is_nullable === 'YES' ? 'nullable' : 'not null'})`);
    });
    
    console.log('\nSchema update completed successfully!');
    
  } catch (error) {
    console.error('Error updating schema:', error);
  } finally {
    process.exit(0);
  }
}

updateSchema();

import 'package:flutter_test/flutter_test.dart';
import 'package:taal_connect/services/http_service.dart';
import 'package:taal_connect/models/user.dart';
import 'dart:convert';

void main() {
  group('Role Update API Tests', () {
    late HttpService httpService;

    setUp(() {
      httpService = HttpService();
      httpService.init();
    });

    tearDown(() {
      httpService.dispose();
    });

    test('should successfully update user role with valid token', () async {
      try {
        // First, create a test user and get a valid token
        final registerResponse = await httpService.post(
          'http://localhost:8000/api/auth/register',
          body: jsonEncode({
            'full_name': 'Role API Test User',
            'email': '<EMAIL>',
            'password': 'test123',
            'phone_number': '1234567890',
            'cnic_number': '12345-1234567-1',
          }),
        );

        expect(registerResponse.statusCode, equals(201));
        final userData = jsonDecode(registerResponse.body);
        final user = User.fromJson(userData);
        
        expect(user.role, equals('customer')); // Default role
        expect(user.token, isNotNull);
        
        // Now try to update the role to singer using the token
        final roleUpdateResponse = await httpService.put(
          'http://localhost:8000/api/auth/update-role',
          headers: {
            'Authorization': 'Bearer ${user.token}',
          },
          body: jsonEncode({
            'role': 'singer',
          }),
        );
        
        expect(roleUpdateResponse.statusCode, equals(200));
        final updatedUserData = jsonDecode(roleUpdateResponse.body);
        final updatedUser = User.fromJson(updatedUserData);
        
        expect(updatedUser.role, equals('singer'));
        expect(updatedUser.id, equals(user.id));
        expect(updatedUser.email, equals(user.email));
        
        print('✅ Successfully updated role from ${user.role} to ${updatedUser.role}');
        
      } catch (e) {
        fail('Role update API test failed: $e');
      }
    });

    test('should fail to update role with invalid token', () async {
      try {
        final roleUpdateResponse = await httpService.put(
          'http://localhost:8000/api/auth/update-role',
          headers: {
            'Authorization': 'Bearer invalid_token_123',
          },
          body: jsonEncode({
            'role': 'singer',
          }),
        );
        
        expect(roleUpdateResponse.statusCode, equals(401));
        final errorData = jsonDecode(roleUpdateResponse.body);
        expect(errorData['message'], contains('token'));
        
        print('✅ Correctly rejected invalid token');
        
      } catch (e) {
        fail('Invalid token test failed: $e');
      }
    });

    test('should fail to update role with invalid role value', () async {
      try {
        // First create a user to get a valid token
        final registerResponse = await httpService.post(
          'http://localhost:8000/api/auth/register',
          body: jsonEncode({
            'full_name': 'Invalid Role Test User',
            'email': '<EMAIL>',
            'password': 'test123',
            'phone_number': '1234567890',
            'cnic_number': '12345-1234567-1',
          }),
        );

        final userData = jsonDecode(registerResponse.body);
        final user = User.fromJson(userData);
        
        // Try to update with invalid role
        final roleUpdateResponse = await httpService.put(
          'http://localhost:8000/api/auth/update-role',
          headers: {
            'Authorization': 'Bearer ${user.token}',
          },
          body: jsonEncode({
            'role': 'invalid_role',
          }),
        );
        
        expect(roleUpdateResponse.statusCode, equals(400));
        final errorData = jsonDecode(roleUpdateResponse.body);
        expect(errorData['message'], contains('Valid role is required'));
        
        print('✅ Correctly rejected invalid role value');
        
      } catch (e) {
        fail('Invalid role test failed: $e');
      }
    });
  });
}
